-- 创建使用资产ID的工艺总览组态
-- 注意：执行前请先运行"上传背景图片资产.sql"获取资产ID

-- 清理旧的组态
DELETE FROM zutai_dashboard WHERE id = 'process_overview_assets';
DELETE FROM zutai_dashboard_list WHERE id = 'process_overview_assets_list';

-- 获取最新的背景图片资产ID
DO $$
DECLARE
    control_room_id TEXT;
    control_panel_id TEXT;
    monitor_desk_id TEXT;
    current_project_id TEXT;
    current_tenant_id TEXT;
    assets_json TEXT;
    panels_json TEXT;
BEGIN
    -- 获取项目ID和租户ID
    SELECT project_id, tenant_id INTO current_project_id, current_tenant_id
    FROM zutai_dashboard_list 
    WHERE project_id IS NOT NULL AND tenant_id IS NOT NULL 
    LIMIT 1;
    
    -- 获取背景图片资产ID
    SELECT id INTO control_room_id FROM zutai_assets WHERE name = 'control-room-main.jpg' ORDER BY create_time DESC LIMIT 1;
    SELECT id INTO control_panel_id FROM zutai_assets WHERE name = 'control-panel.jpg' ORDER BY create_time DESC LIMIT 1;
    SELECT id INTO monitor_desk_id FROM zutai_assets WHERE name = 'monitor-desk.jpg' ORDER BY create_time DESC LIMIT 1;
    
    -- 检查是否找到了资产ID
    IF control_room_id IS NULL OR control_panel_id IS NULL OR monitor_desk_id IS NULL THEN
        RAISE EXCEPTION '未找到背景图片资产，请先执行"上传背景图片资产.sql"';
    END IF;
    
    -- 输出找到的资产ID
    RAISE NOTICE '=== 使用的资产ID ===';
    RAISE NOTICE '主控制室背景: %', control_room_id;
    RAISE NOTICE '控制面板背景: %', control_panel_id;
    RAISE NOTICE '监控台背景: %', monitor_desk_id;
    
    -- 创建组态列表记录
    INSERT INTO zutai_dashboard_list (
        id,
        dashboard_id,
        name,
        detail,
        protect,
        protect_pwd,
        checked,
        type,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        'process_overview_assets_list',
        'process_overview_assets',
        '资产ID工艺总览',
        '使用系统资产ID的专业水厂工艺流程总览监控界面',
        false,
        '',
        true,
        'cloud',
        NOW(),
        COALESCE(current_project_id, 'guazhou_project'),
        COALESCE(current_tenant_id, 'guazhou_tenant')
    );
    
    -- 构建assets JSON
    assets_json := '[
        {
            "id": "' || control_room_id || '",
            "size": 338530,
            "type": "image",
            "createTime": ' || EXTRACT(EPOCH FROM NOW()) * 1000 || ',
            "projectId": "' || COALESCE(current_project_id, '') || '",
            "tenantId": "' || COALESCE(current_tenant_id, '') || '"
        },
        {
            "id": "' || control_panel_id || '",
            "size": 22079,
            "type": "image",
            "createTime": ' || EXTRACT(EPOCH FROM NOW()) * 1000 || ',
            "projectId": "' || COALESCE(current_project_id, '') || '",
            "tenantId": "' || COALESCE(current_tenant_id, '') || '"
        },
        {
            "id": "' || monitor_desk_id || '",
            "size": 17609,
            "type": "image",
            "createTime": ' || EXTRACT(EPOCH FROM NOW()) * 1000 || ',
            "projectId": "' || COALESCE(current_project_id, '') || '",
            "tenantId": "' || COALESCE(current_tenant_id, '') || '"
        }
    ]';
    
    -- 构建panels JSON
    panels_json := '[
        {
            "visible": true,
            "lock": false,
            "name": "主背景图片",
            "type": "buildin/panel/simple-picture",
            "id": "main_background",
            "config": {
                "style": {
                    "height": 1080,
                    "width": 1920,
                    "x": 0,
                    "y": 0,
                    "zIndex": 1,
                    "opacity": 0.7
                },
                "data": {
                    "imageId": "' || control_room_id || '"
                },
                "event": {}
            }
        },
        {
            "visible": true,
            "lock": false,
            "name": "系统标题",
            "type": "buildin/panel/text",
            "id": "main_title",
            "config": {
                "style": {
                    "height": 80,
                    "width": 500,
                    "x": 710,
                    "y": 20,
                    "background": "linear-gradient(135deg, rgba(30, 136, 229, 0.95) 0%, rgba(21, 101, 192, 0.95) 100%)",
                    "color": "#ffffff",
                    "fontSize": "24px",
                    "textAlign": "center",
                    "lineHeight": "80px",
                    "fontWeight": "bold",
                    "borderRadius": "15px",
                    "border": "3px solid rgba(66, 165, 245, 0.8)",
                    "boxShadow": "0 10px 30px rgba(30, 136, 229, 0.4)",
                    "textShadow": "0 2px 4px rgba(0,0,0,0.7)",
                    "backdropFilter": "blur(10px)",
                    "zIndex": 10
                },
                "data": {
                    "text": "🏭 瓜州水厂资产ID工艺总览"
                }
            }
        },
        {
            "visible": true,
            "lock": false,
            "name": "控制面板装饰",
            "type": "buildin/panel/simple-picture",
            "id": "control_panel_decoration",
            "config": {
                "style": {
                    "height": 200,
                    "width": 300,
                    "x": 50,
                    "y": 120,
                    "zIndex": 5,
                    "borderRadius": "15px",
                    "border": "3px solid rgba(66, 165, 245, 0.6)",
                    "boxShadow": "0 8px 24px rgba(0,0,0,0.5)",
                    "opacity": 0.8
                },
                "data": {
                    "imageId": "' || control_panel_id || '"
                },
                "event": {}
            }
        },
        {
            "visible": true,
            "lock": false,
            "name": "监控台装饰",
            "type": "buildin/panel/simple-picture",
            "id": "monitor_desk_decoration",
            "config": {
                "style": {
                    "height": 150,
                    "width": 250,
                    "x": 1570,
                    "y": 120,
                    "zIndex": 5,
                    "borderRadius": "12px",
                    "border": "2px solid rgba(66, 165, 245, 0.5)",
                    "boxShadow": "0 6px 18px rgba(0,0,0,0.4)",
                    "opacity": 0.7
                },
                "data": {
                    "imageId": "' || monitor_desk_id || '"
                },
                "event": {}
            }
        },
        {
            "visible": true,
            "lock": false,
            "name": "说明文本",
            "type": "buildin/panel/text",
            "id": "description_text",
            "config": {
                "style": {
                    "height": 120,
                    "width": 600,
                    "x": 400,
                    "y": 400,
                    "background": "rgba(55, 71, 79, 0.9)",
                    "color": "#e8f5e8",
                    "fontSize": "16px",
                    "padding": "20px",
                    "borderRadius": "10px",
                    "border": "2px solid #546e7a",
                    "zIndex": 10,
                    "backdropFilter": "blur(10px)"
                },
                "data": {
                    "text": "🎨 资产ID工艺总览\\n\\n✅ 使用系统资产管理的背景图片\\n✅ 图片通过资产ID引用，加载稳定\\n✅ 支持图片缓存和版本管理\\n\\n📋 如果看到背景图片，说明资产ID配置成功！"
                }
            }
        }
    ]';
    
    -- 创建组态内容
    INSERT INTO zutai_dashboard (
        id,
        data,
        create_time,
        project_id,
        tenant_id
    ) VALUES (
        'process_overview_assets',
        ('{"pageConfig": {"width": 1920, "height": 1080, "backgroundColor": "#0a1428", "title": "瓜州水厂资产ID工艺总览"}, "assets": ' || assets_json || ', "id": "process_overview_assets", "panels": ' || panels_json || ', "version": "1.0.0", "author": "资产管理工程师", "lastModified": "2024-01-16T02:00:00Z", "description": "使用系统资产ID的专业水厂工艺流程总览监控界面"}')::jsonb,
        NOW(),
        COALESCE(current_project_id, 'guazhou_project'),
        COALESCE(current_tenant_id, 'guazhou_tenant')
    );
    
    RAISE NOTICE '=== 资产ID工艺总览组态创建完成 ===';
    RAISE NOTICE '请刷新组态管理页面，查找"资产ID工艺总览"并预览';
    
END $$;

-- 验证创建结果
SELECT 
    '=== 创建结果验证 ===' as 状态,
    dl.name as 组态名称,
    LENGTH(d.data::text) as JSON大小,
    '字节' as 单位,
    d.create_time as 创建时间
FROM zutai_dashboard_list dl
LEFT JOIN zutai_dashboard d ON dl.dashboard_id = d.id
WHERE dl.id = 'process_overview_assets_list';
